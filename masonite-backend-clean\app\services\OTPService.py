"""OTP Service for handling one-time password operations"""

from datetime import datetime, timedelta
from app.models.User import User
from app.models.OTP import OTP
from masonite.mail import Mail
from masonite.notification import Notification
import re


class OTPService:
    """Service for handling OTP generation, sending, and verification"""

    def __init__(self, mail: Mail = None):
        self.mail = mail

    def send_otp(self, identifier, otp_type="login", delivery_method=None, user_id=None, 
                 ip_address=None, user_agent=None):
        """Send OTP via email or SMS"""
        try:
            # Determine if identifier is email or phone
            is_email = self._is_email(identifier)
            
            # Auto-detect delivery method if not specified
            if delivery_method is None:
                delivery_method = "email" if is_email else "sms"
            
            # For login type, check if user exists
            if otp_type == "login":
                user = self._find_user_by_identifier(identifier)
                if not user:
                    if is_email:
                        raise Exception("User not found. Please register first.")
                    else:
                        raise Exception("Phone number not registered. Please register first or use email.")
                
                user_id = user.id
                
                # For phone numbers, route to email if SMS not available
                if not is_email and delivery_method == "sms":
                    # Try to send to email instead
                    identifier = user.email
                    delivery_method = "email_via_phone"
                    print(f"📱➡️📧 Routing phone OTP to email: {user.email}")

            # Generate OTP
            otp_code, otp_record = OTP.generate_otp(
                identifier=identifier,
                otp_type=otp_type,
                delivery_method=delivery_method,
                user_id=user_id,
                ip_address=ip_address,
                user_agent=user_agent
            )

            # Send OTP based on delivery method
            if delivery_method in ["email", "email_via_phone", "email_fallback"]:
                self._send_email_otp(identifier, otp_code, otp_type)
                sent_to = identifier
                method = delivery_method
            elif delivery_method == "sms":
                try:
                    self._send_sms_otp(identifier, otp_code, otp_type)
                    sent_to = identifier
                    method = "sms"
                except Exception as sms_error:
                    print(f"⚠️ SMS failed, trying email fallback: {str(sms_error)}")
                    # Fallback to email if user exists
                    if user_id:
                        user = User.find(user_id)
                        if user:
                            self._send_email_otp(user.email, otp_code, otp_type)
                            sent_to = user.email
                            method = "email_fallback"
                        else:
                            raise Exception("Unable to send OTP via SMS or email")
                    else:
                        raise Exception("SMS unavailable and no email fallback available")
            else:
                raise Exception(f"Unsupported delivery method: {delivery_method}")

            return {
                'message': f'OTP sent successfully',
                'sentTo': sent_to,
                'method': method,
                'expiresIn': otp_record.time_until_expiry()
            }

        except Exception as e:
            print(f"❌ OTP Send Error: {str(e)}")
            raise e

    def verify_otp(self, identifier, code, otp_type="login"):
        """Verify OTP code"""
        try:
            # Handle phone number to email mapping for verification
            verification_identifier = identifier
            is_email = self._is_email(identifier)
            
            if not is_email:
                # For phone numbers, find the associated email
                user = self._find_user_by_identifier(identifier)
                if user:
                    verification_identifier = user.email
                    print(f"📱➡️📧 Phone OTP verification - using email: {verification_identifier}")

            # Find valid OTP
            otp_record = OTP.find_valid_otp(verification_identifier, otp_type)
            
            if not otp_record:
                return {
                    'valid': False,
                    'message': 'No valid OTP found or OTP expired'
                }

            # Verify the code
            is_valid = otp_record.verify_code(code)
            
            if is_valid and otp_type == "verification":
                # Mark email or phone as verified
                self._mark_as_verified(identifier)

            return {
                'valid': is_valid,
                'message': 'OTP verified successfully' if is_valid else 'Invalid or expired OTP',
                'remainingAttempts': otp_record.get_remaining_attempts() if not is_valid else None
            }

        except Exception as e:
            print(f"❌ OTP Verification Error: {str(e)}")
            return {
                'valid': False,
                'message': 'OTP verification failed'
            }

    def login_with_otp(self, identifier, code):
        """Login user with OTP"""
        try:
            print(f"🔑 OTP Login attempt: {identifier}")

            # Verify OTP first
            verification_result = self.verify_otp(identifier, code, "login")
            
            if not verification_result['valid']:
                raise Exception(verification_result['message'])

            # Find user
            user = self._find_user_by_identifier(identifier)
            if not user:
                raise Exception("User not found after OTP verification")

            # Check if account is active
            if hasattr(user, 'is_active') and not user.is_active:
                raise Exception("Account is deactivated")

            # Update last login and reset security counters
            user.last_login_at = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            if hasattr(user, 'login_attempts'):
                user.login_attempts = 0
            if hasattr(user, 'locked_until'):
                user.locked_until = None
            user.save()

            print(f"✅ OTP Login successful for user: {user.id}")

            return {
                'success': True,
                'user': {
                    'id': user.id,
                    'email': user.email,
                    'name': user.name,
                    'phone': getattr(user, 'phone', None),
                    'email_verified_at': user.email_verified_at,
                    'two_factor_enabled': getattr(user, 'two_factor_enabled', False),
                    'last_login_at': user.last_login_at
                },
                'message': 'Login successful via OTP'
            }

        except Exception as e:
            print(f"❌ OTP Login Error: {str(e)}")
            raise e

    def cleanup_expired_otps(self):
        """Clean up expired and used OTPs"""
        try:
            expired_count = OTP.cleanup_expired()
            used_count = OTP.cleanup_used(older_than_hours=24)
            
            return {
                'message': f'Cleaned up {expired_count} expired and {used_count} used OTPs',
                'expiredCount': expired_count,
                'usedCount': used_count
            }

        except Exception as e:
            print(f"❌ OTP Cleanup Error: {str(e)}")
            raise e

    def _is_email(self, identifier):
        """Check if identifier is an email address"""
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(email_pattern, identifier) is not None

    def _find_user_by_identifier(self, identifier):
        """Find user by email or phone"""
        if self._is_email(identifier):
            return User.where('email', identifier).first()
        else:
            return User.where('phone', identifier).first()

    def _send_email_otp(self, email, otp_code, otp_type):
        """Send OTP via email"""
        try:
            print(f"📧 Sending OTP email to: {email}")
            print(f"🔑 OTP Code: {otp_code} (Type: {otp_type})")
            
            # In a real implementation, this would use Masonite's mail system
            # For now, we'll just log the OTP
            # TODO: Implement actual email sending using Masonite Mail
            # self.mail.to(email).send(OTPMailable(otp_code, otp_type))
            
        except Exception as e:
            print(f"❌ Email sending error: {str(e)}")
            raise e

    def _send_sms_otp(self, phone, otp_code, otp_type):
        """Send OTP via SMS"""
        try:
            print(f"📱 Sending OTP SMS to: {phone}")
            print(f"🔑 OTP Code: {otp_code} (Type: {otp_type})")
            
            # In a real implementation, this would use Vonage or Twilio
            # For now, we'll just log the OTP
            # TODO: Implement actual SMS sending using Masonite Notification system
            # notification.route("vonage", phone).notify(OTPNotification(otp_code, otp_type))
            
            # Simulate SMS failure for testing email fallback
            if phone.startswith("+999"):  # Test number for SMS failure
                raise Exception("SMS service unavailable")
            
        except Exception as e:
            print(f"❌ SMS sending error: {str(e)}")
            raise e

    def _mark_as_verified(self, identifier):
        """Mark email or phone as verified"""
        try:
            is_email = self._is_email(identifier)
            
            if is_email:
                user = User.where('email', identifier).first()
                if user:
                    user.email_verified_at = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    user.save()
                    print(f"✅ Email verified: {identifier}")
            else:
                user = User.where('phone', identifier).first()
                if user:
                    if hasattr(user, 'phone_verified_at'):
                        user.phone_verified_at = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        user.save()
                        print(f"✅ Phone verified: {identifier}")
                    
        except Exception as e:
            print(f"❌ Verification marking error: {str(e)}")

    def get_otp_status(self, identifier, otp_type="login"):
        """Get status of OTP for identifier"""
        try:
            otp_record = OTP.find_valid_otp(identifier, otp_type)
            
            if not otp_record:
                return {
                    'hasValidOTP': False,
                    'message': 'No valid OTP found'
                }

            return {
                'hasValidOTP': True,
                'expiresIn': otp_record.time_until_expiry(),
                'remainingAttempts': otp_record.get_remaining_attempts(),
                'deliveryMethod': otp_record.delivery_method,
                'createdAt': str(otp_record.created_at)
            }

        except Exception as e:
            print(f"❌ OTP Status Error: {str(e)}")
            return {
                'hasValidOTP': False,
                'message': 'Failed to get OTP status'
            }
