"""Account Controller for account management and deletion"""

from masonite.controllers import Controller
from masonite.request import Request
from masonite.response import Response
from masonite.validation import Validator
from masonite.mail import Mail
from app.services.AccountDeletionService import AccountDeletionService
from app.models.AccountDeletionRecord import AccountDeletionRecord


class Account<PERSON>ontroller(Controller):
    """Handle account management operations including GDPR-compliant deletion"""

    def __init__(self, mail: Mail):
        """Initialize account deletion service with mail dependency injection"""
        self.account_deletion_service = AccountDeletionService(mail)

    def request_deletion(self, request: Request, response: Response, validate: Validator):
        """
        POST /api/account/request-deletion
        Request account deletion with data preservation preferences
        """
        # Get authenticated user
        user = request.user()
        if not user:
            return response.json({
                'error': {
                    'statusCode': 401,
                    'name': 'UnauthorizedError',
                    'message': 'Authentication required'
                }
            }, 401)

        # Validate request
        errors = validate.validate({
            'preservePaymentData': 'boolean',
            'preserveTransactionHistory': 'boolean',
            'preserveProfileData': 'boolean',
            'preserveSecurityLogs': 'boolean',
            'customRetentionPeriod': 'integer|min:1|max:365',
            'reason': 'string|max:1000'
        })

        if errors:
            return response.json({
                'error': {
                    'statusCode': 400,
                    'name': 'ValidationError',
                    'message': 'Invalid request data',
                    'details': errors
                }
            }, 400)

        try:
            # Get preferences from request
            preferences = {
                'preservePaymentData': request.input('preservePaymentData', False),
                'preserveTransactionHistory': request.input('preserveTransactionHistory', False),
                'preserveProfileData': request.input('preserveProfileData', False),
                'preserveSecurityLogs': request.input('preserveSecurityLogs', False),
                'customRetentionPeriod': request.input('customRetentionPeriod', 30),
                'reason': request.input('reason')
            }

            print(f"🗑️ Account deletion requested for user: {user.id}")
            print(f"🔧 Deletion preferences: {preferences}")

            # Request account deletion
            result = self.account_deletion_service.request_account_deletion(user.id, preferences)

            return response.json(result, 200)

        except Exception as e:
            print(f"❌ Account deletion request error: {str(e)}")
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'Failed to request account deletion'
                }
            }, 500)

    def deletion_status(self, request: Request, response: Response):
        """
        GET /api/account/deletion-status
        Get account deletion status
        """
        # Get authenticated user
        user = request.user()
        if not user:
            return response.json({
                'error': {
                    'statusCode': 401,
                    'name': 'UnauthorizedError',
                    'message': 'Authentication required'
                }
            }, 401)

        try:
            # Find deletion record
            deletion_record = AccountDeletionRecord.find_by_email(user.email)

            return response.json({
                'hasPendingDeletion': deletion_record is not None and deletion_record.deletion_status == 'pending_confirmation',
                'deletionRecord': {
                    'deletion_id': deletion_record.deletion_id,
                    'status': deletion_record.deletion_status,
                    'requested_at': str(deletion_record.requested_at),
                    'expires_at': str(deletion_record.expires_at) if deletion_record.expires_at else None
                } if deletion_record else None
            }, 200)

        except Exception as e:
            print(f"❌ Deletion status error: {str(e)}")
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'Failed to get deletion status'
                }
            }, 500)

    def cancel_deletion(self, request: Request, response: Response):
        """
        POST /api/account/cancel-deletion
        Cancel pending account deletion
        """
        # Get authenticated user
        user = request.user()
        if not user:
            return response.json({
                'error': {
                    'statusCode': 401,
                    'name': 'UnauthorizedError',
                    'message': 'Authentication required'
                }
            }, 401)

        try:
            # Find pending deletion record
            deletion_record = AccountDeletionRecord.where('email', user.email)\
                .where('deletion_status', 'pending_confirmation')\
                .first()

            if not deletion_record:
                return response.json({
                    'error': {
                        'statusCode': 404,
                        'name': 'NotFoundError',
                        'message': 'No pending deletion request found'
                    }
                }, 404)

            # Cancel the deletion
            deletion_record.cancel_deletion()

            return response.json({
                'message': 'Account deletion request has been cancelled successfully'
            }, 200)

        except Exception as e:
            print(f"❌ Cancel deletion error: {str(e)}")
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'Failed to cancel deletion request'
                }
            }, 500)

    def export_data(self, request: Request, response: Response):
        """
        GET /api/account/export-data
        Export user data as downloadable file
        """
        # Get authenticated user
        user = request.user()
        if not user:
            return response.json({
                'error': {
                    'statusCode': 401,
                    'name': 'UnauthorizedError',
                    'message': 'Authentication required'
                }
            }, 401)

        try:
            print(f"📤 Exporting data for user: {user.id}")

            # Export user data
            user_data = self.account_deletion_service.export_user_data(user.id)

            return response.json(user_data, 200)

        except Exception as e:
            print(f"❌ Data export error: {str(e)}")
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'Failed to export user data'
                }
            }, 500)

    def request_export(self, request: Request, response: Response):
        """
        POST /api/account/request-export
        Request data export via email
        """
        # Get authenticated user
        user = request.user()
        if not user:
            return response.json({
                'error': {
                    'statusCode': 401,
                    'name': 'UnauthorizedError',
                    'message': 'Authentication required'
                }
            }, 401)

        try:
            print(f"📧 Requesting data export via email for user: {user.id}")

            # Request data export
            result = self.account_deletion_service.request_data_export(user.id)

            return response.json(result, 200)

        except Exception as e:
            print(f"❌ Data export request error: {str(e)}")
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'Failed to request data export'
                }
            }, 500)

    def cleanup_expired(self, request: Request, response: Response):
        """
        POST /api/account/cleanup-expired
        Clean up expired deletion records (admin endpoint)
        """
        try:
            print("🧹 Admin cleanup request received")

            # Cleanup expired records
            result = self.account_deletion_service.cleanup_expired_deletion_records()

            return response.json(result, 200)

        except Exception as e:
            print(f"❌ Cleanup error: {str(e)}")
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'Failed to cleanup expired records'
                }
            }, 500)


class AccountPublicController(Controller):
    """Public account controller for confirmation endpoints (no authentication required)"""

    def __init__(self, mail: Mail):
        """Initialize account deletion service with mail dependency injection"""
        self.account_deletion_service = AccountDeletionService(mail)

    def confirm_deletion(self, request: Request, response: Response, validate: Validator):
        """
        POST /api/account/confirm-deletion
        Confirm account deletion with token (public endpoint)
        """
        # Validate request
        errors = validate.validate({
            'token': 'required|string'
        })

        if errors:
            return response.json({
                'error': {
                    'statusCode': 400,
                    'name': 'ValidationError',
                    'message': 'Token is required',
                    'details': errors
                }
            }, 400)

        try:
            token = request.input('token')
            print(f"🔍 Confirming account deletion with token: {token[:10]}...")

            # Confirm deletion
            result = self.account_deletion_service.confirm_account_deletion(token)

            return response.json(result, 200)

        except Exception as e:
            print(f"❌ Deletion confirmation error: {str(e)}")
            return response.json({
                'error': {
                    'statusCode': 400,
                    'name': 'BadRequestError',
                    'message': str(e)
                }
            }, 400)

    def check_preserved_data(self, request: Request, response: Response, validate: Validator):
        """
        POST /api/account/check-preserved-data
        Check if user has preserved data for restoration (public endpoint)
        """
        # Validate request
        errors = validate.validate({
            'email': 'required|email'
        })

        if errors:
            return response.json({
                'error': {
                    'statusCode': 400,
                    'name': 'ValidationError',
                    'message': 'Email is required',
                    'details': errors
                }
            }, 400)

        try:
            email = request.input('email')
            print(f"🔍 Checking preserved data for email: {email}")

            # Check preserved data
            result = self.account_deletion_service.check_preserved_data(email)

            return response.json(result, 200)

        except Exception as e:
            print(f"❌ Check preserved data error: {str(e)}")
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'Failed to check preserved data'
                }
            }, 500)

    def restore_data(self, request: Request, response: Response, validate: Validator):
        """
        POST /api/account/restore-data
        Restore preserved data during signup (public endpoint)
        """
        # Validate request
        errors = validate.validate({
            'userId': 'required|string',
            'email': 'required|email',
            'restorePaymentData': 'boolean',
            'restoreTransactionHistory': 'boolean',
            'restoreProfileData': 'boolean',
            'restoreSecurityLogs': 'boolean'
        })

        if errors:
            return response.json({
                'error': {
                    'statusCode': 400,
                    'name': 'ValidationError',
                    'message': 'Invalid request data',
                    'details': errors
                }
            }, 400)

        try:
            user_id = request.input('userId')
            email = request.input('email')
            restore_options = {
                'restorePaymentData': request.input('restorePaymentData', False),
                'restoreTransactionHistory': request.input('restoreTransactionHistory', False),
                'restoreProfileData': request.input('restoreProfileData', False),
                'restoreSecurityLogs': request.input('restoreSecurityLogs', False)
            }

            print(f"🔄 Restoring data for user: {user_id}")
            print(f"📧 Email: {email}")
            print(f"⚙️ Restore options: {restore_options}")

            # Restore user data
            result = self.account_deletion_service.restore_user_data(user_id, email, restore_options)

            return response.json(result, 200)

        except Exception as e:
            print(f"❌ Data restoration error: {str(e)}")
            return response.json({
                'error': {
                    'statusCode': 400,
                    'name': 'BadRequestError',
                    'message': str(e)
                }
            }, 400)

    def delete_preserved_data(self, request: Request, response: Response, validate: Validator):
        """
        DELETE /api/account/delete-preserved-data
        Permanently delete all preserved data (public endpoint)
        """
        # Validate request
        errors = validate.validate({
            'email': 'required|email'
        })

        if errors:
            return response.json({
                'error': {
                    'statusCode': 400,
                    'name': 'ValidationError',
                    'message': 'Email is required',
                    'details': errors
                }
            }, 400)

        try:
            email = request.input('email')
            print(f"🗑️ Permanently deleting preserved data for email: {email}")

            # Delete preserved data
            result = self.account_deletion_service.delete_preserved_data(email)

            return response.json(result, 200)

        except Exception as e:
            print(f"❌ Delete preserved data error: {str(e)}")
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'Failed to delete preserved data'
                }
            }, 500)
