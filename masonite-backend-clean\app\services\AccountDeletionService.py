"""Account Deletion Service for GDPR-compliant account deletion"""

from datetime import datetime, timedelta
from app.models.User import User
from app.models.AccountDeletionRecord import AccountDeletionRecord
from app.models.Payment import Payment
from masonite.mail import Mail
import json
import secrets


class AccountDeletionService:
    """Service for handling account deletion requests and data preservation"""

    def __init__(self):
        self.mail = Mail()

    def request_account_deletion(self, user_id, preferences):
        """Request account deletion with data preservation preferences"""
        try:
            # Get user details
            user = User.find(user_id)
            if not user:
                raise Exception("User not found")

            # Check if there's already a pending deletion request
            existing_request = AccountDeletionRecord.find_by_email(user.email)
            if existing_request and existing_request.deletion_status == 'pending_confirmation':
                return {
                    'message': 'Account deletion request already pending. Please check your email for confirmation.',
                    'deletionId': existing_request.deletion_id,
                    'confirmationRequired': True
                }

            # Create deletion request
            deletion_record = AccountDeletionRecord.create_deletion_request(
                user_id=user_id,
                email=user.email,
                preferences=preferences
            )

            # Send confirmation email
            self._send_deletion_confirmation_email(user, deletion_record)

            return {
                'message': 'Account deletion request created. Please check your email to confirm.',
                'deletionId': deletion_record.deletion_id,
                'confirmationRequired': True,
                'confirmationToken': deletion_record.confirmation_token
            }

        except Exception as e:
            print(f"❌ Account deletion request error: {str(e)}")
            raise Exception(f"Failed to request account deletion: {str(e)}")

    def confirm_account_deletion(self, token):
        """Confirm account deletion with token"""
        try:
            # Find deletion record by token
            deletion_record = AccountDeletionRecord.find_by_token(token)
            if not deletion_record:
                raise Exception("Invalid or expired confirmation token")

            # Check if token is still valid
            if not deletion_record.is_confirmation_token_valid():
                raise Exception("Confirmation token has expired")

            # Find the user
            user = User.where('email', deletion_record.email).first()
            if not user:
                # User might already be deleted, proceed with record cleanup
                deletion_record.complete_deletion()
                return {
                    'message': 'Account deletion completed',
                    'deletionId': deletion_record.deletion_id
                }

            # Preserve data based on preferences
            preserved_data = self._preserve_user_data(user, deletion_record)

            # Update deletion record with preserved data
            deletion_record.preserved_user_data = preserved_data.get('user_data')
            deletion_record.preserved_payment_data = preserved_data.get('payment_data')
            deletion_record.preserved_transaction_data = preserved_data.get('transaction_data')
            deletion_record.preserved_security_data = preserved_data.get('security_data')

            # Confirm deletion
            deletion_record.confirm_deletion()

            # Actually delete the user account
            self._delete_user_account(user)

            # Complete the deletion
            deletion_record.complete_deletion()

            return {
                'message': 'Account deletion completed successfully',
                'deletionId': deletion_record.deletion_id,
                'preservedDataSummary': self._get_preserved_data_summary(preserved_data)
            }

        except Exception as e:
            print(f"❌ Account deletion confirmation error: {str(e)}")
            raise Exception(f"Failed to confirm account deletion: {str(e)}")

    def export_user_data(self, user_id):
        """Export all user data for download"""
        try:
            user = User.find(user_id)
            if not user:
                raise Exception("User not found")

            # Collect all user data
            user_data = {
                'profile': {
                    'id': user.id,
                    'email': user.email,
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                    'phone': user.phone,
                    'created_at': str(user.created_at),
                    'updated_at': str(user.updated_at),
                    'last_login_at': str(user.last_login_at) if user.last_login_at else None,
                    'email_verified': user.email_verified_at is not None,
                    'two_factor_enabled': user.two_factor_enabled
                },
                'payments': [],
                'security_logs': {
                    'login_attempts': user.login_attempts,
                    'locked_until': str(user.locked_until) if user.locked_until else None,
                    'oauth_providers': user.oauth_providers
                }
            }

            # Get payment data if exists
            try:
                payments = Payment.where('user_id', user_id).get()
                for payment in payments:
                    user_data['payments'].append({
                        'id': payment.id,
                        'amount': payment.amount,
                        'currency': payment.currency,
                        'status': payment.status,
                        'created_at': str(payment.created_at)
                    })
            except:
                pass  # Payment table might not exist

            return user_data

        except Exception as e:
            print(f"❌ Data export error: {str(e)}")
            raise Exception(f"Failed to export user data: {str(e)}")

    def request_data_export(self, user_id):
        """Request data export via email"""
        try:
            user = User.find(user_id)
            if not user:
                raise Exception("User not found")

            # Export data
            user_data = self.export_user_data(user_id)

            # Send email with data (in production, this would be a secure download link)
            self._send_data_export_email(user, user_data)

            return {
                'message': 'Data export has been sent to your email address'
            }

        except Exception as e:
            print(f"❌ Data export request error: {str(e)}")
            raise Exception(f"Failed to request data export: {str(e)}")

    def check_preserved_data(self, email):
        """Check if user has preserved data for restoration"""
        try:
            deletion_record = AccountDeletionRecord.where('email', email)\
                .where('deletion_status', 'completed')\
                .where('expires_at', '>', datetime.now())\
                .first()

            if not deletion_record:
                return {
                    'hasPreservedData': False
                }

            preserved_summary = {}
            if deletion_record.preserved_user_data:
                preserved_summary['profile'] = True
            if deletion_record.preserved_payment_data:
                preserved_summary['payments'] = True
            if deletion_record.preserved_transaction_data:
                preserved_summary['transactions'] = True
            if deletion_record.preserved_security_data:
                preserved_summary['security'] = True

            return {
                'hasPreservedData': True,
                'deletionRecord': {
                    'deletion_id': deletion_record.deletion_id,
                    'completed_at': str(deletion_record.completed_at),
                    'expires_at': str(deletion_record.expires_at)
                },
                'preservedDataSummary': preserved_summary
            }

        except Exception as e:
            print(f"❌ Check preserved data error: {str(e)}")
            return {'hasPreservedData': False}

    def restore_user_data(self, user_id, email, restore_options):
        """Restore preserved data during signup"""
        try:
            # Find deletion record with preserved data
            deletion_record = AccountDeletionRecord.where('email', email)\
                .where('deletion_status', 'completed')\
                .where('expires_at', '>', datetime.now())\
                .first()

            if not deletion_record:
                raise Exception("No preserved data found for this email")

            # Get the new user
            user = User.find(user_id)
            if not user:
                raise Exception("User not found")

            restored_data = {}

            # Restore profile data
            if restore_options.get('restoreProfileData') and deletion_record.preserved_user_data:
                profile_data = deletion_record.preserved_user_data
                if isinstance(profile_data, str):
                    profile_data = json.loads(profile_data)
                
                # Update user with preserved profile data
                user.first_name = profile_data.get('first_name', user.first_name)
                user.last_name = profile_data.get('last_name', user.last_name)
                user.phone = profile_data.get('phone', user.phone)
                user.save()
                
                restored_data['profile'] = True

            # Note: Payment and transaction restoration would require more complex logic
            # and integration with payment systems

            return {
                'message': 'Data restoration completed successfully',
                'restoredData': restored_data
            }

        except Exception as e:
            print(f"❌ Data restoration error: {str(e)}")
            raise Exception(f"Failed to restore user data: {str(e)}")

    def delete_preserved_data(self, email):
        """Permanently delete all preserved data"""
        try:
            deletion_records = AccountDeletionRecord.where('email', email).get()
            
            for record in deletion_records:
                record.delete()

            return {
                'message': f'All preserved data for {email} has been permanently deleted'
            }

        except Exception as e:
            print(f"❌ Delete preserved data error: {str(e)}")
            raise Exception(f"Failed to delete preserved data: {str(e)}")

    def cleanup_expired_deletion_records(self):
        """Clean up expired deletion records"""
        try:
            count = AccountDeletionRecord.cleanup_expired()
            return {
                'message': f'Cleaned up {count} expired deletion records',
                'cleanedCount': count
            }

        except Exception as e:
            print(f"❌ Cleanup error: {str(e)}")
            raise Exception(f"Failed to cleanup expired records: {str(e)}")

    def _preserve_user_data(self, user, deletion_record):
        """Preserve user data based on preferences"""
        preserved_data = {}

        # Preserve profile data
        if deletion_record.preserve_profile_data:
            preserved_data['user_data'] = {
                'first_name': user.first_name,
                'last_name': user.last_name,
                'phone': user.phone,
                'created_at': str(user.created_at),
                'preferences': {}
            }

        # Preserve payment data
        if deletion_record.preserve_payment_data:
            try:
                payments = Payment.where('user_id', user.id).get()
                preserved_data['payment_data'] = [
                    {
                        'amount': payment.amount,
                        'currency': payment.currency,
                        'status': payment.status,
                        'created_at': str(payment.created_at)
                    } for payment in payments
                ]
            except:
                preserved_data['payment_data'] = []

        # Preserve security logs
        if deletion_record.preserve_security_logs:
            preserved_data['security_data'] = {
                'last_login_at': str(user.last_login_at) if user.last_login_at else None,
                'oauth_providers': user.oauth_providers,
                'two_factor_enabled': user.two_factor_enabled
            }

        return preserved_data

    def _delete_user_account(self, user):
        """Actually delete the user account and related data"""
        try:
            # Delete related data that shouldn't be preserved
            # This would include sessions, temporary tokens, etc.
            
            # Clear sensitive fields before deletion
            user.password = None
            user.api_token = None
            user.two_factor_secret = None
            user.recovery_codes = None
            user.save()

            # Delete the user
            user.delete()

        except Exception as e:
            print(f"❌ User account deletion error: {str(e)}")
            raise e

    def _send_deletion_confirmation_email(self, user, deletion_record):
        """Send deletion confirmation email"""
        try:
            # In a real implementation, this would use Masonite's mail system
            print(f"📧 Sending deletion confirmation email to {user.email}")
            print(f"🔗 Confirmation token: {deletion_record.confirmation_token}")
            
            # TODO: Implement actual email sending using Masonite Mail
            # self.mail.to(user.email).send(DeletionConfirmationMailable(deletion_record))
            
        except Exception as e:
            print(f"❌ Email sending error: {str(e)}")

    def _send_data_export_email(self, user, user_data):
        """Send data export email"""
        try:
            print(f"📧 Sending data export email to {user.email}")
            print(f"📊 Data size: {len(str(user_data))} characters")
            
            # TODO: Implement actual email sending using Masonite Mail
            
        except Exception as e:
            print(f"❌ Email sending error: {str(e)}")

    def _get_preserved_data_summary(self, preserved_data):
        """Get summary of preserved data"""
        summary = {}
        
        if preserved_data.get('user_data'):
            summary['profile'] = 'Preserved'
        if preserved_data.get('payment_data'):
            summary['payments'] = f"{len(preserved_data['payment_data'])} records preserved"
        if preserved_data.get('security_data'):
            summary['security'] = 'Preserved'
            
        return summary
