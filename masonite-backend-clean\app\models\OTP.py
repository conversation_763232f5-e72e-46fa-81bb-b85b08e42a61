""" OTP Model """

from masoniteorm.models import Model
from datetime import datetime, timedelta
from passlib.context import CryptContext
import secrets
import uuid


class OTP(Model):
    """OTP Model for handling one-time passwords"""

    __fillable__ = [
        'identifier', 'code_hash', 'otp_type', 'delivery_method',
        'expires_at', 'used', 'used_at', 'attempts', 'max_attempts',
        'user_id', 'metadata', 'ip_address', 'user_agent'
    ]

    __casts__ = {
        'used': 'boolean',
        'attempts': 'integer',
        'max_attempts': 'integer',
        'expires_at': 'datetime',
        'used_at': 'datetime'
    }

    # Password context for hashing OTP codes
    pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

    @classmethod
    def generate_otp(cls, identifier, otp_type="login", delivery_method="email",
                     user_id=None, length=6, expire_minutes=10, max_attempts=3,
                     ip_address=None, user_agent=None):
        """Generate and store OTP"""

        # Generate numeric OTP
        otp_code = ''.join([str(secrets.randbelow(10)) for _ in range(length)])

        # Create OTP record
        expires_at = datetime.now() + timedelta(minutes=expire_minutes)
        otp_record = cls.create({
            'identifier': identifier,
            'code_hash': cls.pwd_context.hash(otp_code),
            'otp_type': otp_type,
            'delivery_method': delivery_method,
            'expires_at': expires_at.strftime('%Y-%m-%d %H:%M:%S'),
            'used': False,
            'attempts': 0,
            'max_attempts': max_attempts,
            'user_id': str(user_id) if user_id else None,
            'metadata': '{}',
            'ip_address': ip_address,
            'user_agent': user_agent
        })

        return otp_code, otp_record

    def verify_code(self, code):
        """Verify OTP code with attempt tracking"""

        # Check if already used
        if self.used:
            return False

        # Check if expired
        expires_at = self.expires_at
        if isinstance(expires_at, str):
            expires_at = datetime.strptime(expires_at, '%Y-%m-%d %H:%M:%S')
        if datetime.now() > expires_at:
            return False

        # Check if max attempts reached
        if self.attempts >= self.max_attempts:
            return False

        # Increment attempts
        self.attempts += 1
        self.save()

        # Verify code
        if self.pwd_context.verify(code, self.code_hash):
            self.used = True
            self.used_at = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            self.save()
            return True

        return False

    def is_valid(self):
        """Check if OTP is still valid"""
        expires_at = self.expires_at
        if isinstance(expires_at, str):
            expires_at = datetime.strptime(expires_at, '%Y-%m-%d %H:%M:%S')
        return (
            not self.used and
            datetime.now() <= expires_at and
            self.attempts < self.max_attempts
        )

    @classmethod
    def find_valid_otp(cls, identifier, otp_type="login"):
        """Find valid OTP for identifier and type"""
        return cls.where('identifier', identifier)\
                  .where('otp_type', otp_type)\
                  .where('used', False)\
                  .where('expires_at', '>', datetime.now())\
                  .where('attempts', '<', cls.query().first().max_attempts if cls.query().first() else 3)\
                  .order_by('created_at', 'desc')\
                  .first()

    @classmethod
    def cleanup_expired(cls):
        """Clean up expired OTP records"""
        expired_otps = cls.where('expires_at', '<', datetime.now()).get()
        count = len(expired_otps)

        for otp in expired_otps:
            otp.delete()

        return count

    @classmethod
    def cleanup_used(cls, older_than_hours=24):
        """Clean up used OTP records older than specified hours"""
        cutoff_time = datetime.now() - timedelta(hours=older_than_hours)
        used_otps = cls.where('used', True)\
                       .where('used_at', '<', cutoff_time)\
                       .get()
        count = len(used_otps)

        for otp in used_otps:
            otp.delete()

        return count

    def mark_as_used(self):
        """Mark OTP as used"""
        self.used = True
        self.used_at = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.save()

    def get_remaining_attempts(self):
        """Get remaining verification attempts"""
        return max(0, self.max_attempts - self.attempts)

    def is_expired(self):
        """Check if OTP is expired"""
        expires_at = self.expires_at
        if isinstance(expires_at, str):
            expires_at = datetime.strptime(expires_at, '%Y-%m-%d %H:%M:%S')
        return datetime.now() > expires_at

    def time_until_expiry(self):
        """Get time until expiry in seconds"""
        if self.is_expired():
            return 0
        expires_at = self.expires_at
        if isinstance(expires_at, str):
            expires_at = datetime.strptime(expires_at, '%Y-%m-%d %H:%M:%S')
        return int((expires_at - datetime.now()).total_seconds())

    def __str__(self):
        return f"OTP({self.identifier}, {self.otp_type}, {self.delivery_method})"
